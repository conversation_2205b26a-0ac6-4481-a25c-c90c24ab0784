{"name": "antd", "version": "5.26.7", "description": "An enterprise-class UI design language and React components implementation", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/ant-design"}, "homepage": "https://ant.design", "repository": {"type": "git", "url": "https://github.com/ant-design/ant-design"}, "bugs": {"url": "https://github.com/ant-design/ant-design/issues"}, "keywords": ["ant", "component", "components", "design", "framework", "frontend", "react", "react-component", "ui"], "sideEffects": ["*.css"], "main": "lib/index.js", "module": "es/index.js", "unpkg": "dist/antd.min.js", "typings": "es/index.d.ts", "files": ["BUG_VERSIONS.json", "dist", "es", "lib", "locale"], "scripts": {"api-collection": "antd-tools run api-collection", "authors": "tsx scripts/generate-authors.ts", "build": "npm run compile && cross-env NODE_OPTIONS='--max-old-space-size=4096' npm run dist", "changelog": "npm run lint:changelog && tsx scripts/print-changelog.ts", "check-commit": "tsx scripts/check-commit.ts", "clean": "antd-tools run clean && rimraf es lib coverage locale dist report.html artifacts.zip oss-artifacts.zip", "clean:lockfiles": "rimraf package-lock.json yarn.lock", "precompile": "npm run prestart", "compile": "npm run clean && antd-tools run compile", "predeploy": "antd-tools run clean && npm run site && cp CNAME _site && npm run test:site", "deploy": "gh-pages -d _site -b gh-pages -f", "deploy:china-mirror": "git checkout gh-pages && git pull origin gh-pages && <NAME_EMAIL>:ant-design/ant-design.git gh-pages -f", "predist": "npm run version && npm run token:statistic && npm run token:meta", "dist": "antd-tools run dist", "format": "biome format --write .", "install-react-16": "npm i --no-save --legacy-peer-deps react@16 react-dom@16 @testing-library/react@12", "install-react-17": "npm i --no-save --legacy-peer-deps react@17 react-dom@17 @testing-library/react@12", "bun-install-react-16": "bun remove react react-dom @testing-library/react && bun add --no-save react@16 react-dom@16 @testing-library/react@12", "bun-install-react-17": "bun remove react react-dom @testing-library/react && bun add --no-save react@17 react-dom@17 @testing-library/react@12", "prelint": "dumi setup", "lint": "npm run version && npm run tsc && npm run lint:script && npm run lint:biome && npm run lint:md && npm run lint:style && npm run lint:changelog", "lint:changelog": "tsx scripts/generate-component-changelog.ts", "lint:deps": "antd-tools run deps-lint", "lint:md": "remark . -f -q", "lint:script": "eslint . --cache", "lint:biome": "biome lint", "lint:style": "tsx scripts/check-cssinjs.tsx", "llms": "tsx scripts/generate-llms.ts", "prepare": "is-ci || husky && dumi setup", "prepublishOnly": "tsx ./scripts/pre-publish.ts", "prettier": "prettier -c --write . --cache", "prettier-import-sort": "npm run prettier -- --plugin=@ianvs/prettier-plugin-sort-imports", "biome": "biome check --write", "pub": "echo 'Please use `npm publish` instead.'", "postpublish": "tsx scripts/post-publish.ts", "presite": "npm run prestart", "site": "dumi build && cp .surgeignore _site && npm run llms", "size-limit": "size-limit", "sort:api-table": "antd-tools run sort-api-table", "sort:package-json": "npx sort-package-json", "prestart": "npm run version && npm run token:statistic && npm run token:meta && npm run lint:changelog", "start": "tsx ./scripts/set-node-options.ts cross-env PORT=8001 dumi dev", "pretest": "npm run version", "test": "jest --config .jest.js --no-cache", "test:all": "sh -e ./scripts/test-all.sh", "test:dekko": "tsx ./tests/dekko/index.test.ts", "test:image": "cross-env MOCK_USE_ID=false jest --config .jest.image.js --no-cache -i -u --forceExit", "test:node": "npm run version && jest --config .jest.node.js --no-cache", "test:package-diff": "antd-tools run package-diff", "test:site": "jest --config .jest.site.js", "test:site-update": "npm run site && npm run test:site -- -u", "test:update": "jest --config .jest.js --no-cache -u", "test:visual-regression": "tsx scripts/visual-regression/build.ts", "test:visual-regression:local": "tsx scripts/visual-regression/local.ts", "token:meta": "tsx scripts/generate-token-meta.ts", "token:statistic": "tsx scripts/collect-token-statistic.ts", "tsc": "tsc --noEmit", "tsc:old": "tsc --noEmit -p tsconfig-old-react.json", "version": "tsx scripts/generate-version.ts"}, "browserslist": ["defaults"], "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@ant-design/colors": "^7.2.1", "@ant-design/cssinjs": "^1.23.0", "@ant-design/cssinjs-utils": "^1.1.3", "@ant-design/fast-color": "^2.0.6", "@ant-design/icons": "^5.6.1", "@ant-design/react-slick": "~1.1.2", "@babel/runtime": "^7.26.0", "@rc-component/color-picker": "~2.0.1", "@rc-component/mutate-observer": "^1.1.0", "@rc-component/qrcode": "~1.0.0", "@rc-component/tour": "~1.15.1", "@rc-component/trigger": "^2.3.0", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.11", "rc-cascader": "~3.34.0", "rc-checkbox": "~3.5.0", "rc-collapse": "~3.9.0", "rc-dialog": "~9.6.0", "rc-drawer": "~7.3.0", "rc-dropdown": "~4.2.1", "rc-field-form": "~2.7.0", "rc-image": "~7.12.0", "rc-input": "~1.8.0", "rc-input-number": "~9.5.0", "rc-mentions": "~2.20.0", "rc-menu": "~9.16.1", "rc-motion": "^2.9.5", "rc-notification": "~5.6.4", "rc-pagination": "~5.1.0", "rc-picker": "~4.11.3", "rc-progress": "~4.0.0", "rc-rate": "~2.13.1", "rc-resize-observer": "^1.4.3", "rc-segmented": "~2.7.0", "rc-select": "~14.16.8", "rc-slider": "~11.1.8", "rc-steps": "~6.0.1", "rc-switch": "~4.1.0", "rc-table": "~7.51.1", "rc-tabs": "~15.6.1", "rc-textarea": "~1.10.1", "rc-tooltip": "~6.4.0", "rc-tree": "~5.13.1", "rc-tree-select": "~5.27.0", "rc-upload": "~4.9.2", "rc-util": "^5.44.4", "scroll-into-view-if-needed": "^3.1.0", "throttle-debounce": "^5.0.2"}, "devDependencies": {"@ant-design/compatible": "^5.1.3", "@ant-design/happy-work-theme": "^1.0.0", "@ant-design/tools": "^18.0.3", "@ant-design/v5-patch-for-react-19": "^1.0.2", "@antfu/eslint-config": "^5.0.0", "@antv/g6": "^4.8.24", "@biomejs/biome": "^2.0.4", "@codecov/webpack-plugin": "^1.4.0", "@codesandbox/sandpack-react": "^2.19.10", "@dnd-kit/core": "^6.2.0", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/css": "^11.13.5", "@emotion/react": "^11.13.5", "@emotion/server": "^11.11.0", "@eslint-react/eslint-plugin": "^1.17.1", "@ianvs/prettier-plugin-sort-imports": "^4.4.0", "@inquirer/prompts": "^7.1.0", "@madccc/duplicate-package-checker-webpack-plugin": "^1.0.0", "@microflash/rehype-figure": "^2.1.1", "@npmcli/run-script": "^9.0.1", "@octokit/rest": "^22.0.0", "@prettier/sync": "^0.6.1", "@qixian.cs/github-contributors-list": "^2.0.2", "@size-limit/file": "^11.1.6", "@stackblitz/sdk": "^1.11.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/adm-zip": "^0.5.6", "@types/ali-oss": "^6.16.11", "@types/cli-progress": "^3.11.6", "@types/css-tree": "^2.3.10", "@types/fs-extra": "^11.0.4", "@types/gtag.js": "^0.0.20", "@types/http-server": "^0.12.4", "@types/isomorphic-fetch": "^0.0.39", "@types/jest": "^30.0.0", "@types/jest-axe": "^3.5.9", "@types/jest-environment-puppeteer": "^5.0.6", "@types/jest-image-snapshot": "^6.4.0", "@types/jquery": "^3.5.31", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.12", "@types/minimist": "^1.2.5", "@types/node": "^24.0.0", "@types/nprogress": "^0.2.3", "@types/pixelmatch": "^5.2.6", "@types/pngjs": "^6.0.5", "@types/prismjs": "^1.26.4", "@types/progress": "^2.0.7", "@types/react": "^19.0.1", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^19.0.2", "@types/react-highlight-words": "^0.20.0", "@types/react-resizable": "^3.0.8", "@types/semver": "^7.5.8", "@types/spinnies": "^0.5.3", "@types/tar": "^6.1.13", "@types/throttle-debounce": "^5.0.2", "@types/warning": "^3.0.3", "adm-zip": "^0.5.16", "ali-oss": "^6.21.0", "antd-img-crop": "^4.23.0", "antd-style": "^3.7.1", "antd-token-previewer": "^2.0.8", "axios": "^1.7.7", "chalk": "^5.0.0", "cheerio": "^1.0.0", "circular-dependency-plugin": "^5.2.2", "cli-progress": "^3.12.0", "cross-env": "^10.0.0", "cross-fetch": "^4.0.0", "css-tree": "^3.1.0", "csstree-validator": "^4.0.1", "cypress-image-diff-html-report": "2.2.0", "dekko": "^0.2.1", "dotenv": "^17.0.0", "dumi": "~2.4.20", "dumi-plugin-color-chunk": "^2.1.0", "env-paths": "^3.0.0", "eslint": "^9.23.0", "eslint-plugin-compat": "^6.0.1", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.14", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "gh-pages": "^6.2.0", "github-slugger": "^2.0.0", "glob": "^11.0.0", "hast-util-to-string": "^3.0.1", "html2sketch": "^1.0.2", "http-server": "^14.1.1", "husky": "^9.1.6", "identity-obj-proxy": "^3.0.0", "immer": "^10.1.1", "is-ci": "^4.0.0", "isomorphic-fetch": "^3.0.0", "jest": "^30.0.0", "jest-axe": "^10.0.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^30.0.0", "jest-image-snapshot": "^6.4.0", "jest-puppeteer": "^11.0.0", "jquery": "^3.7.1", "jsdom": "^26.0.0", "jsonml-to-react-element": "^1.1.11", "jsonml.js": "^0.1.0", "lint-staged": "^16.0.0", "lodash": "^4.17.21", "lunar-typescript": "^1.7.5", "lz-string": "^1.5.0", "minimist": "^1.2.8", "mockdate": "^3.0.5", "node-fetch": "^3.3.2", "node-notifier": "^10.0.1", "open": "^10.1.0", "ora": "^8.1.0", "p-all": "^5.0.0", "package-manager-detector": "^1.0.0", "pixelmatch": "^7.1.0", "pngjs": "^7.0.0", "portfinder": "^1.0.37", "prettier": "^3.4.1", "pretty-format": "^30.0.0", "prismjs": "^1.29.0", "puppeteer": "^24.7.1", "rc-footer": "^0.6.8", "rc-tween-one": "^3.0.6", "rc-virtual-list": "^3.19.1", "react": "^19.1.0", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-draggable": "^4.4.6", "react-fast-marquee": "^1.6.5", "react-highlight-words": "^0.21.0", "react-icons": "^5.4.0", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.13.1", "react-resizable": "^3.0.5", "react-router-dom": "^7.0.1", "react-scan": "^0.4.2", "react-sticky-box": "^2.0.5", "regenerator-runtime": "^0.14.1", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-cli": "^12.0.1", "remark-gfm": "^4.0.0", "remark-lint": "^10.0.0", "remark-lint-no-undefined-references": "^5.0.0", "remark-preset-lint-recommended": "^7.0.0", "remark-rehype": "^11.1.1", "rimraf": "^6.0.1", "runes2": "^1.1.4", "semver": "^7.6.3", "sharp": "^0.34.0", "simple-git": "^3.27.0", "size-limit": "^11.1.6", "spinnies": "^0.5.1", "tar": "^7.4.3", "tsx": "^4.20.3", "typedoc": "^0.28.0", "typescript": "~5.8.2", "vanilla-jsoneditor": "^3.0.0", "vanilla-tilt": "^1.8.1", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "xhr-mock": "^2.5.1"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "size-limit": [{"path": "./dist/antd.min.js", "limit": "510 KiB", "gzip": true}, {"path": "./dist/antd-with-locales.min.js", "limit": "600 KiB", "gzip": true}], "title": "Ant Design", "tnpm": {"mode": "npm"}, "pnpm": {"overrides": {"nwsapi": "2.2.20"}}, "overrides": {"nwsapi": "2.2.20"}, "resolutions": {"nwsapi": "2.2.20"}}