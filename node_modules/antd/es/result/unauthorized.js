"use client";

import * as React from 'react';
const Unauthorized = () => (/*#__PURE__*/React.createElement("svg", {
  width: "251",
  height: "294"
}, /*#__PURE__*/React.createElement("title", null, "Unauthorized"), /*#__PURE__*/React.createElement("g", {
  fill: "none",
  fillRule: "evenodd"
}, /*#__PURE__*/React.createElement("path", {
  fill: "#E4EBF7",
  d: "M0 129v-2C0 58.3 55.6 2.7 124.2 2.7h2c68.6 0 124.2 55.6 124.2 124.1v2.1c0 68.6-55.6 124.2-124.1 124.2h-2.1A124.2 124.2 0 0 1 0 129"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFF",
  d: "M41.4 133a8.2 8.2 0 1 1-16.4-1.7 8.2 8.2 0 0 1 16.4 1.6"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#FFF",
  d: "m38.7 136.4 10.4 5.9m.9 6.2-12.6 10.7",
  strokeWidth: "2"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFF",
  d: "M41.5 161.3a5.6 5.6 0 1 1-11.2-1.2 5.6 5.6 0 0 1 11.2 1.2m17.7-16a5.7 5.7 0 1 1-11.3-1.2 5.7 5.7 0 0 1 11.3 1.2m41.2-115.8H130a4.6 4.6 0 1 0 0-9.1h-29.6a4.6 4.6 0 0 0 0 9.1m11.3 18.3h29.7a4.6 4.6 0 1 0 0-9.2h-29.7a4.6 4.6 0 1 0 0 9.2"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFF",
  d: "M114 29.5h15.8a4.6 4.6 0 1 0 0 9.1H114a4.6 4.6 0 0 0 0-9.1m71.3 108.2a10 10 0 1 1-19.8-2 10 10 0 0 1 19.8 2"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#FFF",
  d: "m180.2 143.8 12.5 7.1m1.1 7.5-15.1 13",
  strokeWidth: "2"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFF",
  d: "M185.6 172a6.8 6.8 0 1 1-13.6-1.4 6.8 6.8 0 0 1 13.5 1.3m18.6-16.6a6.8 6.8 0 1 1-13.6-1.4 6.8 6.8 0 0 1 13.6 1.4"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#FFF",
  d: "M153 194a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0zm73-75.8a2.2 2.2 0 1 1-4.5 0 2.2 2.2 0 0 1 4.4 0zm-9 34.9a2.2 2.2 0 1 1-4.3 0 2.2 2.2 0 0 1 4.4 0zm-39.2-43.3a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0zm18.3-15.3a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0zm6.7 88a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z",
  strokeWidth: "2"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#FFF",
  d: "m215.1 155.3-1.9 20-10.8 6m-27.8-4.7-6.3 9.8H157l-4.5 6.4m23.4-65.5v-15.7m45.6 7.8-12.8 7.9-15.2-7.9V96.7",
  strokeWidth: "2"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#A26EF4",
  d: "M180.7 29.3a29.3 29.3 0 1 1 58.6 0 29.3 29.3 0 0 1-58.6 0"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFF",
  d: "m221.4 41.7-21.5-.1a1.7 1.7 0 0 1-1.7-1.8V27.6a1.7 1.7 0 0 1 1.8-1.7h21.5c1 0 1.8.9 1.8 1.8l-.1 12.3a1.7 1.7 0 0 1-1.7 1.7"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFF",
  d: "M215.1 29.2c0 2.6-2 4.6-4.5 4.6a4.6 4.6 0 0 1-4.5-4.7v-6.9c0-2.6 2-4.6 4.6-4.6 2.5 0 4.5 2 4.4 4.7v6.9zm-4.5-14a6.9 6.9 0 0 0-7 6.8v7.3a6.9 6.9 0 0 0 13.8.1V22a6.9 6.9 0 0 0-6.8-6.9zm-43 53.2h-4a4.7 4.7 0 0 1-4.7-4.8 4.7 4.7 0 0 1 4.7-4.7h4a4.7 4.7 0 0 1 4.7 4.8 4.7 4.7 0 0 1-4.7 4.7"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#5BA02E",
  d: "M168.2 248.8a6.6 6.6 0 0 1-6.6-6.6v-66a6.6 6.6 0 0 1 13.2 0v66a6.6 6.6 0 0 1-6.6 6.6"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#92C110",
  d: "M176.1 248.2a6.6 6.6 0 0 1-6.6-6.6v-33a6.6 6.6 0 1 1 13.3 0v33a6.6 6.6 0 0 1-6.7 6.6"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#F2D7AD",
  d: "M186 293.9h-27.4a3.2 3.2 0 0 1-3.2-3.2v-45.9a3.2 3.2 0 0 1 3.2-3.1H186a3.2 3.2 0 0 1 3.2 3.1v46a3.2 3.2 0 0 1-3.2 3"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFF",
  d: "M82 147.7s6.3-1 17.5-1.3c11.8-.4 17.6 1 17.6 1s3.7-3.8 1-8.3c1.3-12.1 6-32.9.3-48.3-1.1-1.4-3.7-1.5-7.5-.6-1.4.3-7.2-.2-8-.1l-15.3-.4-8-.5c-1.6-.1-4.3-1.7-5.5-.3-.4.4-2.4 5.6-2 16l8.7 35.7s-3.2 3.6 1.2 7"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFC6A0",
  d: "m75.8 73.3-1-6.4 12-6.5s7.4-.1 8 1.2c.8 1.3-5.5 1-5.5 1s-1.9 1.4-2.6 2.5c-1.7 2.4-1 6.5-8.4 6-1.7.3-2.5 2.2-2.5 2.2"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFB594",
  d: "M52.4 77.7S66.7 87 77.4 92c1 .5-2 16.2-11.9 11.8-7.4-3.3-20.1-8.4-21.5-14.5-.7-3.2 2.6-7.6 8.4-11.7M142 80s-6.7 3-13.9 6.9c-3.9 2.1-10.1 4.7-12.3 8-6.2 9.3 3.5 11.2 13 7.5 6.6-2.7 29-12.1 13.2-22.4"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFC6A0",
  d: "m76.2 66.4 3 3.8S76.4 73 73 76c-7 6.2-12.8 14.3-16 16.4-4 2.7-9.7 3.3-12.2 0-3.5-5.1.5-14.7 31.5-26"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFF",
  d: "M64.7 85.1s-2.4 8.4-9 14.5c.7.5 18.6 10.5 22.2 10 5.2-.6 6.4-19 1.2-20.5-.8-.2-6-1.3-8.9-2.2-.9-.2-1.6-1.7-3.5-1l-2-.8zm63.7.7s5.3 2 7.3 13.8c-.6.2-17.6 12.3-21.8 7.8-6.6-7-.8-17.4 4.2-18.6 4.7-1.2 5-1.4 10.3-3"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#E4EBF7",
  d: "M78.2 94.7s.9 7.4-5 13",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.1"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#E4EBF7",
  d: "M87.4 94.7s3.1 2.6 10.3 2.6c7.1 0 9-3.5 9-3.5",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: ".9"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFC6A0",
  d: "m117.2 68.6-6.8-6.1s-5.4-4.4-9.2-1c-3.9 3.5 4.4 2.2 5.6 4.2 1.2 2.1.9 1.2-2 .5-5.7-1.4-2.1.9 3 5.3 2 1.9 7 1 7 1l2.4-3.9z"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFB594",
  d: "m105.3 91.3-.3-11H89l-.5 10.5c0 .4.2.8.6 1 2 1.3 9.3 5 15.8.4.2-.2.4-.5.4-.9"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#5C2552",
  d: "M107.6 74.2c.8-1.1 1-9 1-11.9a1 1 0 0 0-1-1l-4.6-.4c-7.7-1-17 .6-18.3 6.3-5.4 5.9-.4 13.3-.4 13.3s2 3.5 4.3 6.8c.8 1 .4-3.8 3-6a47.9 47.9 0 0 1 16-7"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFC6A0",
  d: "M88.4 83.2s2.7 6.2 11.6 6.5c7.8.3 9-7 7.5-17.5l-1-5.5c-6-2.9-15.4.6-15.4.6s-.6 2-.2 5.5c-2.3 2-1.8 5.6-1.8 5.6s-1-2-2-2.3c-.9-.3-2 0-2.3 2-1 4.6 3.6 5.1 3.6 5.1"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#DB836E",
  d: "m100.8 77.1 1.7-1-1-4.3.7-1.4",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.1"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#552950",
  d: "M105.5 74c0 .8-.4 1.4-1 1.4-.4 0-.8-.7-.8-1.4s.5-1.2 1-1.2.9.6.8 1.3m-8 .2c0 .8-.4 1.3-.9 1.3s-.9-.6-.9-1.3c0-.7.5-1.3 1-1.3s1 .6.9 1.3"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#DB836E",
  d: "M91.1 86.8s5.3 5 12.7 2.3",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.1"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#DB836E",
  d: "M99.8 81.9s-3.6.2-1.5-2.8c1.6-1.5 5-.4 5-.4s1 3.9-3.5 3.2"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#5C2552",
  d: "M102.9 70.6s2.5.8 3.4.7m-12.4.7s2.5-1.2 4.8-1.1",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.5"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#DB836E",
  d: "M86.3 77.4s1 .9 1.5 2c-.4.6-1 1.2-.3 1.9m11.8 2.4s2 .2 2.5-.2",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.1"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#E4EBF7",
  d: "m87.8 115.8 15.7-3m-3.3 3 10-2m-43.7-27s-1.6 8.8-6.7 14M128.3 88s3 4 4 11.7",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.1"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#DB836E",
  d: "M64 84.8s-6 10-13.5 10",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: ".8"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFC6A0",
  d: "m112.4 66-.2 5.2 12 9.2c4.5 3.6 8.9 7.5 11 8.7 4.8 2.8 8.9 3.3 11 1.8 4.1-2.9 4.4-9.9-8.1-15.3-4.3-1.8-16.1-6.3-25.7-9.7"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#DB836E",
  d: "M130.5 85.5s4.6 5.7 11.7 6.2",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: ".8"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#E4EBF7",
  d: "M121.7 105.7s-.4 8.6-1.3 13.6",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.1"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#648BD8",
  d: "M115.8 161.5s-3.6-1.5-2.7-7.1",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.1"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#CBD1D1",
  d: "M101.5 290.2s4.3 2.1 7.4 1c2.9-.9 4.6.7 7.2 1.3 2.5.5 6.9 1 11.7-1.3 0-5.6-7-4-12-6.8-2.6-1.4-3.8-4.7-3.6-8.8h-9.5s-1.4 10.6-1.2 14.6"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#2B0849",
  d: "M101.5 290s2.4 1.4 6.8.7c3-.4 3.7.5 7.5 1 3.7.6 10.8 0 11.9-.8.4 1-.4 2-.4 2s-1.5.7-4.8.9c-2 .1-5.8.3-7.7-.5-1.8-1.4-5.2-2-5.7-.3-4 1-7.4-.3-7.4-.3l-.2-2.6z"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#A4AABA",
  d: "M108.8 276.2h3.1s0 6.7 4.6 8.6c-4.7.6-8.6-2.3-7.7-8.6"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#CBD1D1",
  d: "M57.6 272.5s-2 7.5-4.5 12.4c-1.8 3.7-4.2 7.6 5.5 7.6 6.7 0 9-.5 7.5-6.7-1.5-6.1.3-13.3.3-13.3h-8.8z"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#2B0849",
  d: "M51.5 290s2.2 1.2 6.7 1.2c6.1 0 8.3-1.6 8.3-1.6s.6 1-.6 2.1c-1 .9-3.6 1.6-7.4 1.6-4.2 0-6-.6-6.8-1.2-.9-.5-.7-1.6-.2-2"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#A4AABA",
  d: "M58.5 274.4s0 1.6-.3 3-1 3.1-1.1 4.2c0 1.1 4.5 1.5 5.2 0 .6-1.6 1.3-6.5 1.9-7.3.6-.8-5-2.1-5.7.1"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#7BB2F9",
  d: "m100.9 277 13.3.1s1.3-54.2 1.8-64c.6-9.9 3.8-43.2 1-62.8l-12.4-.7-22.8.8-1.2 10c0 .4-.6.8-.7 1.3 0 .6.4 1.3.3 2-2.3 14-6.3 32.9-8.7 46.4-.1.6-1.2 1-1.4 2.6 0 .3.2 1.6 0 1.8-6.8 18.7-10.8 47.6-14.1 61.6h14.5s2.2-8.6 4-17a3984 3984 0 0 1 23-84.5l3-.5 1 46.1s-.2 1.2.4 2c.5.8-.6 1.1-.4 2.3l.4 1.7-1 11.9c-.4 4.6 0 39 0 39"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#648BD8",
  d: "M77.4 220.4c1.2.1 4-2 7-4.9m23.1 8.4s2.8-1 6.1-3.8",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.1"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#648BD8",
  d: "M108.5 221s2.7-1.2 6-4",
  strokeLinecap: "round",
  strokeLinejoin: "round"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#648BD8",
  d: "M76.1 223.6s2.6-.6 6.5-3.4m4.7-69.4c-.2 3.1.3 8.5-4.3 9m21.8-10.7s.1 14-1.3 15c-2.2 1.6-3 1.9-3 1.9m.5-16.4s0 12.8-1.2 24.3m-4.9 1s7.2-1.6 9.4-1.6m-28.6 31.5-1 4.5s-1.5 1.8-1 3.7c.4 2-1 2-5 15.3-1.7 5.6-4.4 18.5-6.3 27.5l-4 18.4M77 196.7a313.3 313.3 0 0 1-.8 4.8m7.7-50-1.2 10.3s-1 .2-.5 2.3c.1 1.3-2.6 15.6-5.1 30.2M57.6 273h13.2",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.1"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#192064",
  d: "M117.4 147.4s-17-3-35.7.2v4.2s14.6-2.9 35.5-.4l.2-4"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#FFF",
  d: "M107.5 150.4v-5a.8.8 0 0 0-.8-.7H99a.8.8 0 0 0-.7.8v4.8c0 .5.3.9.8.8a140.8 140.8 0 0 1 7.7 0 .8.8 0 0 0 .8-.7"
}), /*#__PURE__*/React.createElement("path", {
  fill: "#192064",
  d: "M106.4 149.4v-3a.6.6 0 0 0-.6-.7 94.1 94.1 0 0 0-5.8 0 .6.6 0 0 0-.7.7v3c0 .4.3.7.7.7h5.7c.4 0 .7-.3.7-.7"
}), /*#__PURE__*/React.createElement("path", {
  stroke: "#648BD8",
  d: "M101.5 274h12.3m-11.1-5v6.5m0-12.4v4.3m-.5-93.4.9 44.4s.7 1.6-.2 2.7c-1 1.1 2.4.7.9 2.2-1.6 1.6.9 1.1 0 3.4-.6 1.5-1 21-1.1 35",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "1.1"
}))));
export default Unauthorized;