import CalendarLocale from "rc-picker/es/locale/sv_SE";
import TimePickerLocale from '../../time-picker/locale/sv_SE';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: 'V<PERSON><PERSON>j datum',
    yearPlaceholder: '<PERSON><PERSON><PERSON>j år',
    quarterPlaceholder: '<PERSON><PERSON><PERSON><PERSON> kvartal',
    monthPlaceholder: '<PERSON><PERSON><PERSON><PERSON> månad',
    weekPlaceholder: 'Välj vecka',
    rangePlaceholder: ['Startdatum', 'Slutdatum'],
    rangeYearPlaceholder: ['Startår', 'Slutår'],
    rangeMonthPlaceholder: ['Startmånad', 'Slutmånad'],
    rangeWeekPlaceholder: ['Startvecka', 'Slutvecka']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;