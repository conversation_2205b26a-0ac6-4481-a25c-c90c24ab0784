import type { ShowcaseTemplate, ShowcaseElement, BackgroundConfig } from './showcase';

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  templates: ShowcaseTemplate[];
}

export interface DefaultTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  thumbnail: string;
  width: number;
  height: number;
  elements: Omit<ShowcaseElement, 'id'>[];
  background: BackgroundConfig;
  tags: string[];
  isPremium: boolean;
}

export interface TemplatePreset {
  name: string;
  description: string;
  config: Partial<ShowcaseTemplate>;
}

export const TEMPLATE_CATEGORIES = {
  APP_STORE: 'app-store',
  PLAY_STORE: 'play-store',
  SOCIAL_MEDIA: 'social-media',
  MARKETING: 'marketing',
  PRESENTATION: 'presentation',
} as const;

export type TemplateCategoryType = typeof TEMPLATE_CATEGORIES[keyof typeof TEMPLATE_CATEGORIES];

export const DEVICE_FRAMES = {
  IPHONE_14: 'iphone-14',
  IPHONE_14_PRO: 'iphone-14-pro',
  PIXEL_7: 'pixel-7',
  SAMSUNG_S23: 'samsung-s23',
  NONE: 'none',
} as const;

export type DeviceFrameType = typeof DEVICE_FRAMES[keyof typeof DEVICE_FRAMES];

export const EXPORT_PRESETS = {
  IOS_APP_STORE: {
    name: 'iOS App Store',
    width: 1242,
    height: 2208,
    format: 'png' as const,
    quality: 1,
    scale: 1,
  },
  ANDROID_PLAY_STORE: {
    name: 'Android Play Store',
    width: 1080,
    height: 1920,
    format: 'png' as const,
    quality: 1,
    scale: 1,
  },
  INSTAGRAM_STORY: {
    name: 'Instagram Story',
    width: 1080,
    height: 1920,
    format: 'jpg' as const,
    quality: 0.9,
    scale: 1,
  },
  FACEBOOK_POST: {
    name: 'Facebook Post',
    width: 1200,
    height: 630,
    format: 'jpg' as const,
    quality: 0.9,
    scale: 1,
  },
  TWITTER_POST: {
    name: 'Twitter Post',
    width: 1200,
    height: 675,
    format: 'jpg' as const,
    quality: 0.9,
    scale: 1,
  },
} as const;

export type ExportPresetType = keyof typeof EXPORT_PRESETS;
