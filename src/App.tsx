import React, { useCallback } from 'react';
import { ConfigProvider, theme, Layout, Button, Typography, Card, message } from 'antd';
import { PlusOutlined, UploadOutlined, DownloadOutlined } from '@ant-design/icons';
import { useDropzone } from 'react-dropzone';
import { useShowcaseStore } from './store/showcaseStore';
import './App.css';

// Suppress React 19 warning for Ant Design v5
const originalError = console.error;
console.error = (...args: any[]) => {
  if (
    typeof args[0] === 'string' &&
    args[0].includes('antd v5 support React is 16 ~ 18')
  ) {
    return;
  }
  originalError.apply(console, args);
};

const { Title } = Typography;
const { Header, Content } = Layout;

function App() {
  const {
    templates,
    addTemplate,
    activeTemplateId,
    setActiveTemplate,
    addImportedFiles,
    isImporting,
    importedFiles
  } = useShowcaseStore();

  const handleAddTestTemplate = () => {
    addTemplate({
      name: `Template ${templates.length + 1}`,
      width: 1242,
      height: 2208,
      elements: [],
      background: { type: 'gradient' },
    });
  };

  const handleTemplateClick = (templateId: string) => {
    setActiveTemplate(templateId);
  };

  // Handle file drop for screenshots
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const imageFiles = acceptedFiles.filter(file =>
      file.type.startsWith('image/')
    );

    if (imageFiles.length === 0) {
      message.error('Please upload image files only');
      return;
    }

    try {
      await addImportedFiles(imageFiles);
      message.success(`${imageFiles.length} screenshot(s) imported successfully`);
    } catch (error) {
      message.error('Failed to import screenshots');
      console.error('Import error:', error);
    }
  }, [addImportedFiles]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.webp']
    },
    multiple: true,
    noClick: true,
  });

  const handleImportClick = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = 'image/*';
    input.onchange = (e) => {
      const files = Array.from((e.target as HTMLInputElement).files || []);
      if (files.length > 0) {
        onDrop(files);
      }
    };
    input.click();
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        token: {
          colorPrimary: '#667eea',
          borderRadius: 8,
          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        },
      }}
    >
      <div {...getRootProps()}>
        <input {...getInputProps()} />

        <Layout style={{
          height: '100vh',
          background: isDragActive ? '#f6ffed' : '#f5f5f5',
          transition: 'background-color 0.2s ease'
        }}>
          <Header style={{
            background: '#fff',
            borderBottom: '1px solid #e8e8e8',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 24px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '32px',
                height: '32px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '16px',
              }}>
                S2S
              </div>
              <Title level={4} style={{ margin: 0, color: '#1f2937' }}>
                Screenshots2Show
              </Title>
            </div>

            <div style={{ display: 'flex', gap: '12px' }}>
              <Button
                icon={<UploadOutlined />}
                onClick={handleImportClick}
                loading={isImporting}
                style={{
                  background: isDragActive ? '#52c41a' : undefined,
                  borderColor: isDragActive ? '#52c41a' : undefined,
                }}
              >
                {isDragActive ? 'Drop Screenshots' : 'Import Screenshots'}
              </Button>
              <Button icon={<PlusOutlined />} onClick={handleAddTestTemplate}>New Template</Button>
              <Button icon={<DownloadOutlined />} disabled={templates.length === 0}>Export</Button>
            </div>
          </Header>

        <Content style={{ padding: '24px', overflow: 'auto', position: 'relative' }}>
          {isDragActive && (
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'rgba(82, 196, 26, 0.1)',
              border: '2px dashed #52c41a',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1000,
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#52c41a',
            }}>
              📸 Drop your screenshots here!
            </div>
          )}

          <div style={{ marginBottom: '24px' }}>
            <Title level={2} style={{ margin: '0 0 8px 0' }}>Your Showcase Templates</Title>
            <p style={{ margin: 0, color: '#6b7280' }}>
              Create professional app store screenshots • {templates.length} templates • {importedFiles.length} imported files
            </p>
          </div>

          {templates.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '60px 20px',
              background: '#fafafa',
              borderRadius: '8px',
              border: '2px dashed #d9d9d9'
            }}>
              <Title level={4} style={{ color: '#8c8c8c' }}>No templates yet</Title>
              <p style={{ color: '#8c8c8c', marginBottom: '20px' }}>
                Create your first template to get started
              </p>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddTestTemplate}>
                Create Template
              </Button>
            </div>
          ) : (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
              gap: '24px',
            }}>
              {templates.map((template) => (
                <Card
                  key={template.id}
                  hoverable
                  onClick={() => handleTemplateClick(template.id)}
                  style={{
                    borderRadius: '12px',
                    border: activeTemplateId === template.id ? '2px solid #667eea' : '1px solid #d9d9d9'
                  }}
                  cover={
                    <div style={{
                      height: '200px',
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '14px',
                    }}>
                      {activeTemplateId === template.id ? '✓ Selected' : 'Click to Edit'}
                    </div>
                  }
                >
                  <Card.Meta
                    title={template.name}
                    description={`${template.width} × ${template.height} • ${template.elements?.length || 0} elements`}
                  />
                </Card>
              ))}
            </div>
          )}

          {importedFiles.length > 0 && (
            <div style={{ marginTop: '32px' }}>
              <Title level={3} style={{ margin: '0 0 16px 0' }}>Imported Screenshots</Title>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
                gap: '16px',
              }}>
                {importedFiles.map((file) => (
                  <Card
                    key={file.id}
                    size="small"
                    cover={
                      <img
                        src={file.preview}
                        alt={file.name}
                        style={{
                          width: '100%',
                          height: '120px',
                          objectFit: 'cover',
                        }}
                      />
                    }
                  >
                    <Card.Meta
                      title={file.name}
                      description={`${(file.size / 1024).toFixed(1)} KB`}
                    />
                  </Card>
                ))}
              </div>
            </div>
          )}
        </Content>
        </Layout>
      </div>
    </ConfigProvider>
  );
}

export default App;
