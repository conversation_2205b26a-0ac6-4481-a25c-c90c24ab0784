import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// Temporary simple types to avoid import issues
interface SimpleTemplate {
  id: string;
  name: string;
  width: number;
  height: number;
  elements: any[];
  background: any;
  createdAt: Date;
  updatedAt: Date;
}

// Simple ID generator
const generateId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

interface ImportedFile {
  id: string;
  file: File;
  preview: string;
  name: string;
  size: number;
  type: string;
  uploadedAt: Date;
}

interface ShowcaseState {
  // Templates
  templates: SimpleTemplate[];
  activeTemplateId: string | null;

  // Files
  importedFiles: ImportedFile[];

  // UI State
  isImporting: boolean;
  isExporting: boolean;
  selectedElementId: string | null;

  // Actions
  addTemplate: (template: Omit<SimpleTemplate, 'id' | 'createdAt' | 'updatedAt'>) => void;
  setActiveTemplate: (id: string | null) => void;
  setSelectedElement: (elementId: string | null) => void;

  // File Actions
  addImportedFiles: (files: File[]) => Promise<void>;
  removeImportedFile: (id: string) => void;
  clearImportedFiles: () => void;
}

export const useShowcaseStore = create<ShowcaseState>()(
  devtools(
    (set) => ({
      // Initial State
      templates: [],
      activeTemplateId: null,
      importedFiles: [],
      isImporting: false,
      isExporting: false,
      selectedElementId: null,

      // Template Actions
      addTemplate: (template) => {
        const newTemplate: SimpleTemplate = {
          ...template,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          templates: [...state.templates, newTemplate],
          activeTemplateId: newTemplate.id,
        }));
      },

      setActiveTemplate: (id) => {
        set({ activeTemplateId: id });
      },

      setSelectedElement: (elementId) => {
        set({ selectedElementId: elementId });
      },

      // File Actions
      addImportedFiles: async (files) => {
        set({ isImporting: true });

        try {
          const importedFiles: ImportedFile[] = await Promise.all(
            files.map(async (file) => {
              const preview = await new Promise<string>((resolve) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target?.result as string);
                reader.readAsDataURL(file);
              });

              return {
                id: generateId(),
                file,
                preview,
                name: file.name,
                size: file.size,
                type: file.type,
                uploadedAt: new Date(),
              };
            })
          );

          set((state) => ({
            importedFiles: [...state.importedFiles, ...importedFiles],
          }));
        } finally {
          set({ isImporting: false });
        }
      },

      removeImportedFile: (id) => {
        set((state) => ({
          importedFiles: state.importedFiles.filter((file) => file.id !== id),
        }));
      },

      clearImportedFiles: () => {
        set({ importedFiles: [] });
      },
    }),
    {
      name: 'showcase-store',
    }
  )
);
